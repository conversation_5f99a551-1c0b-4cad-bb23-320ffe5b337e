{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/desk/ceshi/my-new-project/src/app/page.tsx"], "sourcesContent": ["export default function Home() {\n  return (\n    <main className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-6\">\n            Welcome to My New Project\n          </h1>\n          <p className=\"text-xl text-gray-600 mb-8\">\n            A modern Next.js application with Supabase integration\n          </p>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\">\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <h2 className=\"text-xl font-semibold text-gray-800 mb-3\">\n                Next.js 15\n              </h2>\n              <p className=\"text-gray-600\">\n                Latest version with App Router and Turbopack\n              </p>\n            </div>\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <h2 className=\"text-xl font-semibold text-gray-800 mb-3\">\n                React 19\n              </h2>\n              <p className=\"text-gray-600\">\n                Modern React with latest features\n              </p>\n            </div>\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <h2 className=\"text-xl font-semibold text-gray-800 mb-3\">\n                Supabase\n              </h2>\n              <p className=\"text-gray-600\">\n                Backend-as-a-Service with authentication\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </main>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6WAAC;QAAK,WAAU;kBACd,cAAA,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6WAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,6WAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,6WAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,6WAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C", "debugId": null}}]}